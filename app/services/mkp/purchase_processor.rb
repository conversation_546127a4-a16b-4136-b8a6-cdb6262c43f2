# frozen_string_literal: true

module Mkp
  # process ctions post order confirmation
  module PurchaseProcessor
    extend self

    MI_MOTO_STORE_ID = 43

    def create_order!(customer, checkout_cart, payments = nil, current_store_id = nil, points_payments = nil)
      AuditHelper.with_whodunnit('system-job:mkp_purchase_processor', 'create_order!') do
        raise ArgumentError, 'Please, provide a valid checkout cart!' \
          unless checkout_cart.present?

        @checkout_cart = checkout_cart

        order = Order.new do |order|
          customer ||=  checkout_cart.customer
          order.customer = customer
          order.store_id = current_store_id

          if customer.present?
            if order.customer.provider == 'macro_premia'
              order.id_cobis = order.customer.uuid
            end
          end

          if checkout_cart.coupon.present?
            order.coupon = Coupon::Network.find(checkout_cart.coupon[:id])
            order.coupon_discount = checkout_cart.coupon_total_discount
          end

          order.ip = checkout_cart.ip
          order.network = checkout_cart.network
          order.purchase_id = checkout_cart.purchase_id
          order.title = checkout_cart.title
          order.data = checkout_cart.data
          order.points = checkout_cart.points
          order.points_money = checkout_cart.points_money
          order.points_uuid = checkout_cart.points_uuid
          order.biometry_id = checkout_cart.biometry_id

          if checkout_cart.promotion.present?
            order.promotion = checkout_cart.promotion
          end
        end

        associate_payments(order, checkout_cart, payments, points_payments)
        order.save!

        suborder_generator(checkout_cart).each do |suborder|
          order.suborders << suborder
        end

        confirmed = payments.none?(&:pending?) || PENDING_PAYMENTS_AS_CONFIRMED
        run_hooks_before_confirmation(order, confirmed)
        run_hooks_after_confirmation(order) if confirmed
        order
      end
    end

    def associate_payments(order, checkout_cart, payments, points_payments)
      if payments&.any? { |pay| pay.present? } && payments.any? { |pay| pay.apply_bonified? } && checkout_cart.store.percentage_fee > 0
        order.bonified_amount = checkout_cart.percentage_fee
      end

      order.payments << payments if payments.present?
      order.payments << points_payments if points_payments.present?
    end

    def run_hooks_before_confirmation(order, confirmed = true)
        Rails.logger.info "-- purchase_processor: procesando orden ##{order.id}"
        Rails.logger.info '-- purchase_processor: terminó asignación de pagos'

        # run_updates
        update_products_stock(order.suborders) if confirmed
        update_coupons_stock(order.suborders)
        order.solr_index
        # run_updates end
        # run_updates(order)
        Rails.logger.info '-- purchase_processor: terminó run_updates'

        run_update_supplier_stock(order) if confirmed
        Rails.logger.info '-- purchase_processor: terminó run_update_supplier_stock'
    end

    def run_hooks_after_confirmation(order)
      run_async_tasks(order)
      Rails.logger.info '-- purchase_processor: terminó run_async_tasks'

      run_invoice_item_creator(order)
      Rails.logger.info '-- purchase_processor: terminó run_invoice_item_creator'

      run_notifications(order) unless order.store.id == MI_MOTO_STORE_ID
      Rails.logger.info '-- purchase_processor: terminó run_notifications'
    end

    def run_hooks_update_stock(order)
      update_products_stock(order.suborders)
    end

    def create_shipments!(order, destination_address, choosen_delivery_options)
      unless choosen_delivery_options.present?
        raise ArgumentError, 'You need to provide delivery options!'
      end

      order.suborders.each do |suborder|
        transaction_type = suborder.items.first.product[:transaction_type]
        if transaction_type == 5
          manager = Mkp::ShipmentsManager.new(order, suborder, suborder.items, nil, OpenStruct.new({ email: order.customer.email }))
          manager.create_shipment('virtual')
        else
          chosen_option = choosen_delivery_options[suborder.shop_id].first
          manager = Mkp::ShipmentsManager.new(order, suborder, suborder.items, chosen_option, destination_address)
          manager.default_shipment_status = "pending" if order.payments.any?(&:pending?) # suborder no tiene asociado payments
          shipment_kind = chosen_option.pickup ? 'pickup' : 'delivery'
          manager.create_shipment(shipment_kind)
        end
      end
    end

    def confirm_pickit_order(quotation_id, order) # checkout controller
      shipment = order.suborders.first.shipment
      customer = order.customer
      if order.is_paid?
        request_body = {
          cotizacionId: quotation_id,
          tokenId: PICKIT_TOKEN_ID,
          courierId: '1',
          tipoOperacion: '1',
          observaciones: "Numero de Orden Avenida #{order.id}",
          numeroOrden: shipment.id,
          dataDireccionAlternativa: '',
          motivoCambio: '1',
          dataCliente: {
            nombre: customer.first_name,
            apellido: customer.last_name,
            email: customer.email,
            dni: order.payment.gateway_data[:payer][:identification][:number]
          }
        }
        response = Gateways::Shipments::Pickit.confirm_order(request_body.to_json)
        gateway_object_id = response[:TransaccionId]
        status = 'in_process'
        Gateways::Labels::Lion.purchase(shipment)
      else
        gateway_object_id = quotation_id
        status = 'unfulfilled'
      end
      charge = shipment.shipment_kind == "pickup" ? 0 : Gateways::Shipments::Pickit::SHIPPING_PRICE
      shipment.update_attributes(
        gateway: 'Pickit',
        status: status,
        gateway_object_id: gateway_object_id,
        charged_amount: charge
      )
      Gateways::Shipments::Pickit.process_label(shipment) if order.is_paid?
    end

    def generate_coupon_discount_for_suborder(order)
      return generate_item_coupon_discount(order) if order.store_id == 41

      generate_proportional_coupon_discount(order)
    end

    private

    def suborder_generator(checkout_cart)
      checkout_cart.items_per_shop.map do |shop, items|
        taxes = generate_taxes_for_suborder(checkout_cart, shop)
        title = generate_title_for_suborder(checkout_cart, items)
        items = generate_order_items(items, checkout_cart.store)
        shop_commission = calculate_shop_commission(checkout_cart.store, shop, items)

        suborder = Suborder.new do |suborder|
          suborder.shop = shop
          suborder.taxes = taxes
          suborder.title = title
          suborder.fulfilled_by_gp = shop.fulfilled_by_gp?
          suborder.shop_commission = shop_commission
          suborder.items << items
        end

        suborder
      end
    end

    def generate_taxes_for_suborder(checkout_cart, shop)
      checkout_cart.taxes(checkout_cart.customer_address.try(:state), shop)
    end

    def generate_title_for_suborder(checkout_cart, items)
      ::Mkp::PurchaseTitleizer.perform(items.first.title, items.size, checkout_cart.network)
    end

    def generate_order_items(items, store)
      @store = store
      items.map(&method(:generate_order_item))
    end

    def generate_order_item(item)
      OrderItem.new do |order_item|
        order_item.product = item.product
        order_item.variant = item.variant
        order_item.price = item.price
        order_item.sale_price = item.sale_price
        order_item.currency = item.currency
        order_item.quantity = item.quantity
        order_item.points = item.product.transaction_type == 'points' ? item.product.points_price : item.points
        order_item.point_equivalent = item.product.get_points_equivalence(@store)
        order_item.iva = item.product.iva
        order_item.payments << item.payment if item.payment.present?
      end
    end

    def calculate_shop_commission(store, shop, items)
      commission = 0

      # base shop commission
      if shop.setting.present?
        if shop.setting.commercial_agreement.present?
          if shop.setting.commercial_agreement['sale_commission'].present?
            shop_commission = shop.setting.commercial_agreement['sale_commission'].to_d
            commission = shop_commission if shop_commission > 0
          end
        end
      end

      # shop commission for store
      shop_store = shop.shop_stores.find_by(store: store)
      if shop_store.present?
        if shop_store.commission.present?
          commission = shop_store.commission if shop_store.commission > 0
        end
      end

      # shop commission for categories
      if items.any? { |i| i.product.category.commission(store).present? }
        commission = items.min { |i| i.product.category.commission(store) }.product.category.commission(store)
      end

      commission
    end

    def run_invoice_item_creator(order)
      InvoiceItem.create_from(order)
    end

    def run_update_supplier_stock(order)
      SupplierStock.create_from(order)
    end

    def run_updates(order)
      update_products_stock(order.suborders)
      update_coupons_stock(order.suborders)

    end

    def run_hooks_after_cancelation
      run_revert_updates(order)
      Rails.logger.info '-- purchase_processor: terminó run_updates'

      run_revert_update_supplier_stock(order)
      Rails.logger.info '-- purchase_processor: terminó run_revert_update_supplier_stock'
    end

    def run_revert_updates(order)
      #WIP descomentar metodos que aceptan parametro revert
      update_products_stock(order.suborders, true)
      update_coupons_stock(order.suborders, true)

      order.solr_index
    end

    def run_revert_update_supplier_stock(order)
      # Falta implementar el metodo revert_from(order)
      #SupplierStock.revert_from(order)
    end

    def update_products_stock(suborders)
      suborders.each do |suborder|
        suborder.items.group_by(&:product).each do |product, order_items|
          order_items.each do |order_item|
            variant = order_item.variant

            variant.with_lock do
              if suborder.order.is_paid?
                variant.decrease_quantity(order_item.quantity)
              else
                variant.increase_reserved_quantity(order_item.quantity)
              end

              if variant.quantity.zero?
                Mkp::InventoryMailer.delay_for(30.seconds).out_of_stock_reminder(variant.id)
                product.set_variant_visibility
              end
            end
          end

          if product.external_objects.present?
            Mkp::Integration::RemoteSyncWorker.perform_async(product.id, order_items.map(&:variant_id))
          end
        end
      end
    end

    # def update_products_stock(suborders, revert = false)
    #   suborders.each do |suborder|
    #     suborder.items.group_by(&:product).each do |product, order_items|
    #       order_items.each do |order_item|
    #         variant = order_item.variant
    #         if suborder.order.is_paid?
    #           if revert
    #             variant.increase_quantity(order_item.quantity)
    #           else
    #             variant.decrease_quantity(order_item.quantity)
    #           end
    #         else
    #           if revert
    #             variant.decrease_reserved_quantity(order_item.quantity)
    #           else
    #             variant.increase_reserved_quantity(order_item.quantity)
    #           end
    #         end
    #         if variant.quantity.zero?
    #           Mkp::InventoryMailer.delay_for(30.seconds).out_of_stock_reminder(variant.id)
    #           product.set_variant_visibility
    #         end
    #       end
    #
    #       if product.external_objects.present?
    #         Mkp::Integration::RemoteSyncWorker.perform_async(product.id, order_items.map(&:variant_id))
    #       end
    #     end
    #   end
    # end

    # def update_coupons_stock(suborders, revert = false)
      #order = suborders.first.order
      #suborders.each do |suborder|
      #  if revert
      #    suborder.coupon&.increase_properties_to_order!(order)
      #  else
      #    suborder.coupon&.decrease_properties_to_order!(order)
      #  end
      #end
      #if order.coupon.present?
      #  if revert
      #    order.coupon.increase_properties_to_order!(order)
      #  else
      #    order.coupon.decrease_properties_to_order!(order)
      #  end
      #end
    #end

    def update_coupons_stock(suborders)
      order = suborders.first.order

      suborders.each do |suborder|
        suborder.coupon&.decrease_properties_to_order!(order)
      end

      # order.coupon.decrease_properties_to_order!(order) if order.coupon.present?
    end

    def run_notifications(order)
      if order.store.name == 'sportclub'
        notify_sport_club(order)
      elsif order.store_id != 20
        notify_purchase_to_customer(order)
        notify_purchase_to_network_admins(order)
      end

      if order.is_paid?
        if order.store_id.eql?(20) # si es el ciudad
          notify_purchase_to_customer(order)
          notify_purchase_to_network_admins(order)
        end
        notify_paid_to_customer(order)
        notify_paid_to_sellers(order)
        notify_paid_to_network_admins(order)
        notify_affiliate_networks(order)
        notify_purchase_to_webhooks(order)
      end
    end

    def notify_paid_to_customer(order)
      if order.payment.try(:payment_method).eql?('ticket')
        Mkp::OrderMailer.delay_for(55.seconds).customer_paid_confirmation(order)
      end
    end

    def notify_paid_to_sellers(order)
      order.suborders.each do |suborder|
        suborder.shop.merchants.each do |merchant|
          Mkp::OrderMailer.delay_for(35.seconds).shop_paid_confirmation(suborder, merchant)
        end

        if (additional_emails = suborder.shop.notify_purchases_emails).present?
          Mkp::OrderMailer.delay_for(35.seconds).shop_paid_confirmation(suborder, additional_emails)
        end
      end

      if (fulfilled_suborders = order.suborders.select(&:fulfilled_by_gp?)).present?
        merchants = Mkp::Shop.find(Network[order.network].default_shop_id).merchants
        merchants.each do |merchant|
          Mkp::OrderMailer.delay_for(55.seconds).shop_fulfillment_notification(
            fulfilled_suborders, merchant
          )
        end
      end
    end

    def notify_sport_club(order)
      Mkp::VoucherMailer.delay_for(35.seconds).customer_voucher(order)
    end

    # def notify_purchase_to_customer(order)
    #   layout = Mkp::Store::BNA_STORE_IDS.include?(order.store.id) ? 'v5/mailer/bna/purchase_confirmation' : nil
    #   Mkp::OrderMailer.delay_for(35.seconds).customer_purchase_confirmation(order, layout)
    # end

    def notify_purchase_to_customer(order)
      return unless order.present?

      preloaded_order = Mkp::Order.includes(items: { variant: :product }).find_by(id: order.id) || order

      is_elegi_mas = preloaded_order.items.any? do |item|
        item.variant&.product&.shop_id.to_i == 2256
      end

      layout = if is_elegi_mas
        'v5/mailer/bna/elegi-mas-purchase-confirmation'
      elsif Mkp::Store::BNA_STORE_IDS.include?(preloaded_order.store.id)
        'v5/mailer/bna/purchase_confirmation'
      else
        nil
      end

      if is_elegi_mas
        Mkp::OrderMailer.delay_for(35.seconds).customer_purchase_confirmation_elegi_mas(preloaded_order, layout)
      else
        Mkp::OrderMailer.delay_for(35.seconds).customer_purchase_confirmation(preloaded_order, layout)
      end
    end
    
    def notify_purchase_to_network_admins(order)
      Pioneer::Admin.by_network(order.network).to_be_notified.each do |network_admin|
        Mkp::OrderMailer.delay_for(35.seconds).network_admin_purchase_confirmation(order, network_admin)
      end
    end

    def notify_paid_to_network_admins(order)
      Pioneer::Admin.by_network(order.network).to_be_notified.each do |network_admin|
        Mkp::OrderMailer.delay_for(55.seconds).network_admin_paid_confirmation(order, network_admin)
      end
    end

    def notify_affiliate_networks(order)
      return if order.affiliate_id.blank?

      "NetworkAffiliates::#{order.affiliate_type}Worker".constantize.perform_async(order.id, order.affiliate_id)
    end

    def notify_purchase_to_webhooks(order)
      order.suborders.each do |suborder|
        next unless (webhooks = suborder.shop.setting.webhooks).present?

        webhooks.each do |webhook|
          Mkp::Integration::NotifyPurchaseToWebhooksWorker.perform_async(suborder.id, webhook)
        end
      end
    end

    def run_invoice_creator; end

    def run_async_tasks(order)
      reset_cart_status(order)
      delete_cart_items(order)
      follow_purchase_related_brands(order)
      # newsletter is not working properly, fix this later
      # subscribe_to_buyers_newsletter(order)
    end

    def reset_cart_status(order)
      cart = order.customer.get_or_create_regular_cart(order.network)
      cart.reset_status!
    end

    def delete_cart_items(order)
      Mkp::DeleteItemsFromCartWorker.perform_async(order.id)
    end

    def follow_purchase_related_brands(order)
      # We don't have available to follow a brand by customer
      # Mkp::FollowPurchaseRelatedBrandsWorker.perform_async(order.id)
    end

    def subscribe_to_buyers_newsletter(order)
      cart = Mkp::Cart.find_by_purchase_id(order.purchase_id)
      return unless cart&.newsletter

      customer = order.customer

      options = {}.tap do |hash|
        hash[:email_address] = customer.email
        hash[:status] = 'subscribed'
        if customer.first_name && customer.last_name
          hash[:merge_fields] = { FNAME: customer.first_name, LNAME: customer.last_name }
        end
      end

      store_name = order.store.name
      # NewsletterSubscriptionWorker.perform_async(options, 'buyers', store_name) if ( store_name.eql?('avenida') || store_name.eql?('tiendalanueva') )
    end

    def generate_proportional_coupon_discount(order)
      order.suborders.each do |suborder|
        suborder.update(coupon_discount: order.coupon.calculate_suborder(suborder))
      end
    end

    def generate_item_coupon_discount(order)
      selected_id = order.suborders.flat_map(&:items).max_by { |item| item.product.price }.suborder.id

      order.suborders.each do |suborder|
        coupon_amount = suborder.id == selected_id ? order.coupon_discount : 0.0

        suborder.update(coupon_discount: coupon_amount)
      end
    end
  end
end
